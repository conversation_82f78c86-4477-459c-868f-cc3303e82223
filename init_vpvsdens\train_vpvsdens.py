#!/usr/bin/env python3
"""
Main training script for init_vpvsdens - Vp, Vs, and Density Prediction

This script provides a unified training interface for all three curve types:
- Vp (compressional/sonic AC)
- Vs (shear/DTS) 
- Density (DEN/RHOB)

Usage:
    python train_vpvsdens.py --curve AC --template standard_vp --data_file A1.hdf5
    python train_vpvsdens.py --curve DTS --template standard_shear --data_file A1.hdf5
    python train_vpvsdens.py --curve DEN --template standard_density --data_file A1.hdf5
"""

import argparse
import sys
import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

# Import init_vpvsdens components
from init_vpvsdens.core.configs.curves import get_curve_config, get_supported_curves
from init_vpvsdens.core.configs.training import (
    get_training_template, get_integrated_template, list_available_templates
)
from init_vpvsdens.core.training.losses import CurveSpecificLossFactory
from init_vpvsdens.core.training.checkpoint import get_device, CheckpointManager

# Import original training infrastructure
sys.path.append(str(Path(__file__).parent.parent / 'init_vp_pred'))
from train_vp_improved import VpPipelineConfig, VpDataProcessor, VpTrainingManager


def setup_logging(log_level: str = 'INFO') -> logging.Logger:
    """Setup logging configuration"""
    # Setup console handler with UTF-8 encoding
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, log_level.upper()))

    # Setup file handler with UTF-8 encoding
    file_handler = logging.FileHandler('vpvsdens_training.log', encoding='utf-8')
    file_handler.setLevel(getattr(logging, log_level.upper()))

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)

    # Setup logger
    logger = logging.getLogger(__name__)
    logger.setLevel(getattr(logging, log_level.upper()))
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return logger


def validate_arguments(args: argparse.Namespace) -> bool:
    """Validate command line arguments"""
    logger = logging.getLogger(__name__)
    
    # Validate curve
    supported_curves = get_supported_curves()
    if args.curve not in supported_curves:
        logger.error(f"Unsupported curve: {args.curve}")
        logger.error(f"Supported curves: {supported_curves}")
        return False
    
    # Validate template
    available_templates = list_available_templates()
    if args.template:
        if args.template not in available_templates['integrated_templates']:
            logger.error(f"Unknown template: {args.template}")
            logger.error(f"Available templates: {available_templates['integrated_templates']}")
            return False
    
    # Validate data file
    if not Path(args.data_file).exists():
        logger.error(f"Data file not found: {args.data_file}")
        return False
    
    return True


def create_training_config(args: argparse.Namespace) -> Dict[str, Any]:
    """Create training configuration from arguments"""
    logger = logging.getLogger(__name__)
    
    # Get curve configuration
    curve_config = get_curve_config(args.curve)
    logger.info(f"Target curve: {curve_config['name']} ({curve_config['unit']})")
    
    # Get training template
    if args.template:
        integrated_template = get_integrated_template(args.template)
        training_config = get_training_template(integrated_template['training_template'])
        logger.info(f"Using integrated template: {args.template}")
    else:
        # Use default template based on curve type
        template_map = {
            'AC': 'vp_training',
            'VP': 'vp_training', 
            'DTS': 'shear_training',
            'VS': 'shear_training',
            'DEN': 'density_training',
            'RHOB': 'density_training'
        }
        template_name = template_map.get(args.curve, 'vp_training')
        training_config = get_training_template(template_name)
        logger.info(f"Using default template: {template_name}")
    
    # Override with command line arguments
    if args.learning_rate:
        training_config['learning_rate'] = args.learning_rate
    if args.batch_size:
        training_config['batch_size'] = args.batch_size
    if args.epochs:
        training_config['max_epochs'] = args.epochs
    
    # Add data file and output directory
    training_config['data_file'] = args.data_file
    training_config['output_dir'] = args.output_dir
    training_config['target_curve'] = args.curve
    
    return training_config


def setup_loss_function(curve: str, config: Dict[str, Any]) -> Any:
    """Setup loss function for the target curve"""
    logger = logging.getLogger(__name__)
    
    # Get loss configuration from training config
    loss_config = config.get('loss_config', {})
    custom_params = loss_config.get('custom_params', {})
    
    # Create curve-specific loss function
    loss_fn = CurveSpecificLossFactory.create_loss(curve, custom_params)
    
    logger.info(f"Loss function created for {curve}:")
    logger.info(f"  Type: {loss_fn.loss_type}")
    logger.info(f"  Physics constraints: {loss_fn.physics_constraints}")
    logger.info(f"  Constraint weight: {loss_fn.constraint_weight}")
    
    return loss_fn


def run_training(config: Dict[str, Any]) -> Dict[str, Any]:
    """Execute the training process"""
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 60)
    logger.info("STARTING VPVSDENS TRAINING")
    logger.info("=" * 60)
    logger.info(f"Target curve: {config['target_curve']}")
    logger.info(f"Data file: {config['data_file']}")
    logger.info(f"Output directory: {config['output_dir']}")
    logger.info(f"Learning rate: {config['learning_rate']}")
    logger.info(f"Batch size: {config['batch_size']}")
    logger.info(f"Max epochs: {config['max_epochs']}")
    
    # Setup device
    device = get_device()
    logger.info(f"Using device: {device}")
    
    # Setup checkpoint manager
    checkpoint_manager = CheckpointManager(
        save_dir=config['output_dir'],
        model_name=f"{config['target_curve']}_model",
        keep_best=True,
        keep_last_n=3
    )
    
    # Setup loss function
    loss_fn = setup_loss_function(config['target_curve'], config)
    
    # Create VpPipelineConfig for compatibility with existing infrastructure
    vp_config = VpPipelineConfig()
    
    # Override with our configuration
    vp_config.config['training']['learning_rate'] = config['learning_rate']
    vp_config.config['training']['batch_size'] = config['batch_size']
    vp_config.config['training']['epochs'] = config['max_epochs']
    
    # Create data processor
    data_processor = VpDataProcessor(vp_config)
    
    # Create training manager
    training_manager = VpTrainingManager(vp_config, Path(config['output_dir']))
    
    logger.info("Training infrastructure setup complete")
    
    # Note: Full training implementation would require adapting the existing
    # VpTrainingManager to work with the new loss functions and configurations
    # This is a framework for the complete implementation
    
    logger.info("⚠️  Training framework setup complete")
    logger.info("📝 Note: Full training implementation requires integration")
    logger.info("    of existing VpTrainingManager with new loss functions")
    
    return {
        'status': 'framework_ready',
        'config': config,
        'device': str(device),
        'checkpoint_manager': checkpoint_manager,
        'loss_function': loss_fn
    }


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Train MWLT models for Vp, Vs, and Density prediction",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Train Vp (AC) model
  python train_vpvsdens.py --curve AC --template standard_vp --data_file init_vp_pred/A1.hdf5
  
  # Train Vs (DTS) model  
  python train_vpvsdens.py --curve DTS --template standard_shear --data_file init_vp_pred/A1.hdf5
  
  # Train Density model
  python train_vpvsdens.py --curve DEN --template standard_density --data_file init_density_base/A1.hdf5
  
  # Custom training parameters
  python train_vpvsdens.py --curve AC --learning_rate 0.0001 --batch_size 16 --epochs 100
        """
    )
    
    # Required arguments
    parser.add_argument('--curve', required=True, 
                       help='Target curve to predict (AC, DTS, DEN, etc.)')
    parser.add_argument('--data_file', required=True,
                       help='Path to HDF5 data file')
    
    # Optional arguments
    parser.add_argument('--template', 
                       help='Training template to use (standard_vp, standard_shear, standard_density)')
    parser.add_argument('--output_dir', default='./vpvsdens_results',
                       help='Output directory for results (default: ./vpvsdens_results)')
    parser.add_argument('--learning_rate', type=float,
                       help='Learning rate override')
    parser.add_argument('--batch_size', type=int,
                       help='Batch size override')
    parser.add_argument('--epochs', type=int,
                       help='Number of epochs override')
    parser.add_argument('--log_level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level (default: INFO)')
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging(args.log_level)
    
    # Validate arguments
    if not validate_arguments(args):
        sys.exit(1)
    
    # Create output directory
    Path(args.output_dir).mkdir(parents=True, exist_ok=True)
    
    try:
        # Create training configuration
        config = create_training_config(args)
        
        # Run training
        results = run_training(config)
        
        logger.info("=" * 60)
        logger.info("TRAINING COMPLETED")
        logger.info("=" * 60)
        logger.info(f"Status: {results['status']}")
        logger.info(f"Results saved to: {args.output_dir}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())
