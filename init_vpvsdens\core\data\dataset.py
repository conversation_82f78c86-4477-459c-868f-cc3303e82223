"""
Dataset module for General Well Log Transformer - import wrapper

This module imports the unchanged GeneralWellLogDataset from the original
vp_predictor codebase to avoid duplication while maintaining the specialized
directory structure for vpvsdens.
"""

# Import the unchanged dataset from original codebase
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'init_vp_pred'))
from vp_predictor.core.dataset import GeneralWellLogDataset

# Re-export for convenience
__all__ = ['GeneralWellLogDataset']
