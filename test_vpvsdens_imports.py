#!/usr/bin/env python3
"""
Test script to verify import dependencies in init_vpvsdens package

This script systematically tests all imports to identify broken dependencies
and provide a clear status report of what's working and what needs fixing.
"""

import sys
import traceback
from pathlib import Path

def test_basic_imports():
    """Test basic Python imports"""
    print("=" * 60)
    print("TESTING BASIC IMPORTS")
    print("=" * 60)
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__} imported successfully")
        print(f"  CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"  GPU: {torch.cuda.get_device_name(0)}")
    except ImportError as e:
        print(f"✗ PyTorch import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__} imported successfully")
    except ImportError as e:
        print(f"✗ NumPy import failed: {e}")
        return False
    
    return True

def test_init_vpvsdens_imports():
    """Test init_vpvsdens package imports"""
    print("\n" + "=" * 60)
    print("TESTING INIT_VPVSDENS IMPORTS")
    print("=" * 60)
    
    # Test main package import
    try:
        import init_vpvsdens
        print("✓ init_vpvsdens package imported successfully")
    except Exception as e:
        print(f"✗ init_vpvsdens package import failed: {e}")
        traceback.print_exc()
        return False
    
    # Test core config imports
    try:
        from init_vpvsdens.core.configs.curves import get_curve_config, CURVE_CONFIGURATIONS
        print("✓ curves configuration imported successfully")
        print(f"  Available curves: {list(CURVE_CONFIGURATIONS.keys())}")
    except Exception as e:
        print(f"✗ curves configuration import failed: {e}")
        traceback.print_exc()
    
    try:
        from init_vpvsdens.core.configs.training import get_training_template, TRAINING_TEMPLATES
        print("✓ training configuration imported successfully")
        print(f"  Available templates: {list(TRAINING_TEMPLATES.keys())}")
    except Exception as e:
        print(f"✗ training configuration import failed: {e}")
        traceback.print_exc()
    
    # Test loss functions
    try:
        from init_vpvsdens.core.training.losses import (
            GeneralWellLogLoss, create_vp_loss, create_density_loss, create_shear_loss
        )
        print("✓ loss functions imported successfully")
    except Exception as e:
        print(f"✗ loss functions import failed: {e}")
        traceback.print_exc()
    
    # Test checkpoint functionality
    try:
        from init_vpvsdens.core.training.checkpoint import save_checkpoint, load_checkpoint
        print("✓ checkpoint functions imported successfully")
    except Exception as e:
        print(f"✗ checkpoint functions import failed: {e}")
        traceback.print_exc()
    
    return True

def test_original_package_imports():
    """Test imports from original packages"""
    print("\n" + "=" * 60)
    print("TESTING ORIGINAL PACKAGE IMPORTS")
    print("=" * 60)
    
    # Test init_vp_pred imports
    try:
        from init_vp_pred.vp_predictor.core.dataset import GeneralWellLogDataset
        print("✓ GeneralWellLogDataset imported successfully")
    except Exception as e:
        print(f"✗ GeneralWellLogDataset import failed: {e}")
        traceback.print_exc()
    
    try:
        from init_vp_pred.vp_predictor.core.normalizer import GeneralDataNormalizer
        print("✓ GeneralDataNormalizer imported successfully")
    except Exception as e:
        print(f"✗ GeneralDataNormalizer import failed: {e}")
        traceback.print_exc()
    
    try:
        from init_vp_pred.vp_predictor.core.transformer import GeneralWellLogTransformer
        print("✓ GeneralWellLogTransformer imported successfully")
    except Exception as e:
        print(f"✗ GeneralWellLogTransformer import failed: {e}")
        traceback.print_exc()
    
    try:
        from init_vp_pred.vp_predictor.core.training import GeneralTrainingManager
        print("✓ GeneralTrainingManager imported successfully")
    except Exception as e:
        print(f"✗ GeneralTrainingManager import failed: {e}")
        traceback.print_exc()
    
    return True

def test_api_imports():
    """Test API imports"""
    print("\n" + "=" * 60)
    print("TESTING API IMPORTS")
    print("=" * 60)
    
    try:
        from init_vpvsdens.api.vs_predictor import VsPredictor, predict_vs
        print("✓ VsPredictor API imported successfully")
    except Exception as e:
        print(f"✗ VsPredictor API import failed: {e}")
        traceback.print_exc()
    
    return True

def test_configuration_functionality():
    """Test that configurations work correctly"""
    print("\n" + "=" * 60)
    print("TESTING CONFIGURATION FUNCTIONALITY")
    print("=" * 60)
    
    try:
        from init_vpvsdens.core.configs.curves import get_curve_config
        
        # Test getting curve configurations
        for curve in ['AC', 'DTS', 'DEN', 'RHOB']:
            try:
                config = get_curve_config(curve)
                print(f"✓ {curve} configuration: {config['name']} ({config['unit']})")
            except Exception as e:
                print(f"✗ {curve} configuration failed: {e}")
    
    except Exception as e:
        print(f"✗ Configuration functionality test failed: {e}")
        traceback.print_exc()
    
    try:
        from init_vpvsdens.core.configs.training import get_training_template
        
        # Test getting training templates
        for template in ['vp_training', 'shear_training', 'density_training']:
            try:
                config = get_training_template(template)
                print(f"✓ {template} template: {config['description']}")
            except Exception as e:
                print(f"✗ {template} template failed: {e}")
    
    except Exception as e:
        print(f"✗ Training template functionality test failed: {e}")
        traceback.print_exc()

def main():
    """Run all import tests"""
    print("INIT_VPVSDENS IMPORT DEPENDENCY TEST")
    print("=" * 60)
    print(f"Python version: {sys.version}")
    print(f"Working directory: {Path.cwd()}")
    
    # Run all tests
    basic_ok = test_basic_imports()
    if not basic_ok:
        print("\n❌ CRITICAL: Basic imports failed. Cannot proceed.")
        return False
    
    vpvsdens_ok = test_init_vpvsdens_imports()
    original_ok = test_original_package_imports()
    api_ok = test_api_imports()
    
    # Test functionality
    test_configuration_functionality()
    
    # Summary
    print("\n" + "=" * 60)
    print("IMPORT TEST SUMMARY")
    print("=" * 60)
    print(f"Basic imports: {'✓ PASS' if basic_ok else '✗ FAIL'}")
    print(f"init_vpvsdens imports: {'✓ PASS' if vpvsdens_ok else '✗ FAIL'}")
    print(f"Original package imports: {'✓ PASS' if original_ok else '✗ FAIL'}")
    print(f"API imports: {'✓ PASS' if api_ok else '✗ FAIL'}")
    
    if all([basic_ok, vpvsdens_ok, original_ok, api_ok]):
        print("\n🎉 ALL IMPORTS SUCCESSFUL!")
        return True
    else:
        print("\n⚠️  SOME IMPORTS FAILED - See details above")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
