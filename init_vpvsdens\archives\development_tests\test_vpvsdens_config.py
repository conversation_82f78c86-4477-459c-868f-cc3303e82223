#!/usr/bin/env python3
"""
Test script to verify configuration system functionality in init_vpvsdens

This script tests curve configurations, training templates, loss functions,
and integrated templates to ensure they work correctly for Vp, Vs, and density prediction.
"""

import sys
import torch
import numpy as np
from pathlib import Path

def test_curve_configurations():
    """Test curve configuration functionality"""
    print("=" * 60)
    print("TESTING CURVE CONFIGURATIONS")
    print("=" * 60)
    
    try:
        from init_vpvsdens.core.configs.curves import (
            get_curve_config, get_supported_curves, validate_curve_combination,
            CURVE_CONFIGURATIONS
        )
        
        # Test key curves for Vp, Vs, Density
        key_curves = ['AC', 'DTS', 'DEN', 'RHOB', 'VS']
        
        for curve in key_curves:
            try:
                config = get_curve_config(curve)
                print(f"✓ {curve}: {config['name']} ({config['unit']})")
                print(f"  Range: {config['physics_range']}")
                print(f"  Normalization: {config['normalization']['method']}")
                print(f"  Type: {config['type']}")
                
                # Validate required fields
                required_fields = ['name', 'unit', 'type', 'physics_range', 'normalization']
                for field in required_fields:
                    if field not in config:
                        print(f"  ⚠️  Missing required field: {field}")
                
            except Exception as e:
                print(f"✗ {curve} configuration failed: {e}")
        
        # Test supported curves
        supported = get_supported_curves()
        print(f"\n✓ Supported curves ({len(supported)}): {supported}")
        
        return True
        
    except Exception as e:
        print(f"✗ Curve configuration test failed: {e}")
        return False

def test_training_templates():
    """Test training template functionality"""
    print("\n" + "=" * 60)
    print("TESTING TRAINING TEMPLATES")
    print("=" * 60)
    
    try:
        from init_vpvsdens.core.configs.training import (
            get_training_template, get_integrated_template, get_curve_training_hints,
            TRAINING_TEMPLATES, INTEGRATED_TEMPLATES
        )
        
        # Test key training templates
        key_templates = ['vp_training', 'shear_training', 'density_training']
        
        for template in key_templates:
            try:
                config = get_training_template(template)
                print(f"✓ {template}:")
                print(f"  Target: {config['target_curve']}")
                print(f"  Learning rate: {config['learning_rate']}")
                print(f"  Batch size: {config['batch_size']}")
                print(f"  Max epochs: {config['max_epochs']}")
                print(f"  Description: {config['description']}")
                
                # Validate required fields
                required_fields = ['target_curve', 'learning_rate', 'batch_size', 'max_epochs']
                for field in required_fields:
                    if field not in config:
                        print(f"  ⚠️  Missing required field: {field}")
                
            except Exception as e:
                print(f"✗ {template} template failed: {e}")
        
        # Test integrated templates
        key_integrated = ['standard_vp', 'standard_shear', 'standard_density']
        
        print(f"\nTesting integrated templates:")
        for template in key_integrated:
            try:
                config = get_integrated_template(template)
                print(f"✓ {template}: {config['description']}")
                print(f"  Model: {config['model_template']}")
                print(f"  Training: {config['training_template']}")
                print(f"  Data: {config['data_template']}")
            except Exception as e:
                print(f"✗ {template} integrated template failed: {e}")
        
        # Test training hints
        print(f"\nTesting training hints:")
        for curve in ['AC', 'DTS', 'DEN']:
            try:
                hints = get_curve_training_hints(curve)
                print(f"✓ {curve} hints: LR range {hints['suggested_lr_range']}")
            except Exception as e:
                print(f"✗ {curve} hints failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Training template test failed: {e}")
        return False

def test_loss_functions():
    """Test loss function functionality"""
    print("\n" + "=" * 60)
    print("TESTING LOSS FUNCTIONS")
    print("=" * 60)
    
    try:
        from init_vpvsdens.core.training.losses import (
            GeneralWellLogLoss, create_vp_loss, create_density_loss, create_shear_loss,
            CurveSpecificLossFactory
        )
        
        # Test creating loss functions for each curve type
        curves_and_creators = [
            ('AC', create_vp_loss),
            ('DTS', create_shear_loss),
            ('DEN', create_density_loss)
        ]
        
        for curve, creator in curves_and_creators:
            try:
                loss_fn = creator()
                print(f"✓ {curve} loss function created successfully")
                print(f"  Type: {type(loss_fn).__name__}")
                print(f"  Target curve: {loss_fn.target_curve}")
                print(f"  Loss type: {loss_fn.loss_type}")
                print(f"  Physics constraints: {loss_fn.physics_constraints}")
                
                # Test with dummy data
                batch_size, seq_len = 2, 640
                predictions = torch.randn(batch_size, 1, seq_len)
                targets = torch.randn(batch_size, 1, seq_len)
                
                loss_components = loss_fn(predictions, targets)
                print(f"  Test loss: {loss_components['total_loss'].item():.4f}")
                
            except Exception as e:
                print(f"✗ {curve} loss function failed: {e}")
        
        # Test factory method
        print(f"\nTesting CurveSpecificLossFactory:")
        for curve in ['AC', 'DTS', 'DEN']:
            try:
                loss_fn = CurveSpecificLossFactory.create_loss(curve)
                print(f"✓ Factory created {curve} loss: {loss_fn.loss_type}")
            except Exception as e:
                print(f"✗ Factory {curve} loss failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Loss function test failed: {e}")
        return False

def test_checkpoint_functionality():
    """Test checkpoint save/load functionality"""
    print("\n" + "=" * 60)
    print("TESTING CHECKPOINT FUNCTIONALITY")
    print("=" * 60)
    
    try:
        from init_vpvsdens.core.training.checkpoint import (
            save_checkpoint, load_checkpoint, CheckpointManager, get_device
        )
        
        # Test device detection
        device = get_device()
        print(f"✓ Device detected: {device}")
        
        # Test checkpoint save/load with dummy data
        test_dir = Path("test_checkpoints")
        test_dir.mkdir(exist_ok=True)
        
        # Create dummy state
        dummy_state = {
            'model_state_dict': {'weight': torch.randn(10, 5)},
            'epoch': 42,
            'loss': 0.123,
            'optimizer_state_dict': {'param_groups': [{'lr': 0.001}]}
        }
        
        checkpoint_path = test_dir / "test_checkpoint.pt"
        
        # Test save
        success = save_checkpoint(dummy_state, str(checkpoint_path))
        if success:
            print(f"✓ Checkpoint saved successfully")
        else:
            print(f"✗ Checkpoint save failed")
            return False
        
        # Test load
        loaded_state, epoch, loss = load_checkpoint(str(checkpoint_path), device)
        if loaded_state is not None:
            print(f"✓ Checkpoint loaded successfully")
            print(f"  Epoch: {epoch}, Loss: {loss}")
        else:
            print(f"✗ Checkpoint load failed")
            return False
        
        # Test CheckpointManager
        manager = CheckpointManager(str(test_dir), "test_model")
        success = manager.save_checkpoint(dummy_state, 1, is_best=True)
        if success:
            print(f"✓ CheckpointManager save successful")
        else:
            print(f"✗ CheckpointManager save failed")
        
        # Cleanup
        import shutil
        shutil.rmtree(test_dir, ignore_errors=True)
        
        return True
        
    except Exception as e:
        print(f"✗ Checkpoint functionality test failed: {e}")
        return False

def test_api_functionality():
    """Test API functionality"""
    print("\n" + "=" * 60)
    print("TESTING API FUNCTIONALITY")
    print("=" * 60)
    
    try:
        from init_vpvsdens.api.vs_predictor import VsPredictor
        
        # Test VsPredictor initialization (without actual model)
        print("✓ VsPredictor class imported successfully")
        print("  Note: Actual prediction testing requires trained model")
        
        # Test that the class has expected methods
        expected_methods = ['predict', 'predict_batch', 'get_model_info', 'validate_input']
        for method in expected_methods:
            if hasattr(VsPredictor, method):
                print(f"  ✓ Method {method} available")
            else:
                print(f"  ✗ Method {method} missing")
        
        return True
        
    except Exception as e:
        print(f"✗ API functionality test failed: {e}")
        return False

def main():
    """Run all configuration tests"""
    print("INIT_VPVSDENS CONFIGURATION SYSTEM TEST")
    print("=" * 60)
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"Working directory: {Path.cwd()}")
    
    # Run all tests
    tests = [
        ("Curve Configurations", test_curve_configurations),
        ("Training Templates", test_training_templates),
        ("Loss Functions", test_loss_functions),
        ("Checkpoint Functionality", test_checkpoint_functionality),
        ("API Functionality", test_api_functionality)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"\n✗ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("CONFIGURATION TEST SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 ALL CONFIGURATION TESTS PASSED!")
        print("The init_vpvsdens configuration system is functional!")
    else:
        print("\n⚠️  SOME CONFIGURATION TESTS FAILED")
        print("See details above for specific issues.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
