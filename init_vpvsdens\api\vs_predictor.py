"""
Vs (Shear Velocity) Predictor API

This module provides a specialized API for DTS (shear sonic) predictions,
configured with the 'standard_shear' template for optimal shear velocity
estimation.

Example usage:
    predictor = VsPredictor(model_path='path/to/model.pt')
    vs_predictions = predictor.predict(well_data)
"""

import torch
import numpy as np
from typing import Dict, Any, Optional, Union, List
import logging

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'init_vp_pred'))
from vp_predictor.api.predictor import GeneralWellLogPredictor
from ..core.configs.training import get_integrated_template

logger = logging.getLogger(__name__)


class VsPredictor:
    """
    Specialized predictor for shear velocity (DTS/Vs) estimation
    
    This is a thin wrapper over GeneralWellLogPredictor configured specifically
    for shear sonic predictions using the 'standard_shear' template.
    """
    
    def __init__(
        self,
        model_path: Optional[str] = None,
        config_override: Optional[Dict[str, Any]] = None,
        device: Optional[torch.device] = None
    ):
        """
        Initialize Vs predictor
        
        Args:
            model_path: Path to trained model checkpoint
            config_override: Optional configuration overrides
            device: Computation device
        """
        # Get the standard shear configuration
        try:
            shear_config = get_integrated_template('standard_shear')
            logger.info("Loaded 'standard_shear' template for DTS prediction")
        except Exception as e:
            logger.warning(f"Failed to load standard_shear template: {e}")
            logger.info("Using fallback configuration for DTS")
            shear_config = {
                'model_template': 'vp_prediction',
                'training_template': 'shear_training',
                'data_template': 'standard_sequence'
            }
        
        # Apply any overrides
        if config_override:
            shear_config.update(config_override)
        
        # Initialize the general predictor with DTS-specific configuration
        self.predictor = GeneralWellLogPredictor(
            target_curve='DTS',
            model_path=model_path,
            config_override=shear_config,
            device=device
        )
        
        logger.info("VsPredictor initialized for DTS (shear sonic) predictions")
    
    def predict(
        self,
        input_data: Union[np.ndarray, torch.Tensor, Dict[str, np.ndarray]],
        return_confidence: bool = False,
        denormalize: bool = True
    ) -> Union[np.ndarray, tuple]:
        """
        Predict shear velocity (DTS) values
        
        Args:
            input_data: Input well log data
            return_confidence: Whether to return prediction confidence
            denormalize: Whether to denormalize outputs to physical units
            
        Returns:
            DTS predictions in μs/ft, optionally with confidence estimates
        """
        logger.info("Predicting DTS (shear sonic) values...")
        
        predictions = self.predictor.predict(
            input_data=input_data,
            return_confidence=return_confidence,
            denormalize=denormalize
        )
        
        if return_confidence:
            vs_pred, confidence = predictions
            logger.info(f"DTS prediction completed. Shape: {vs_pred.shape}, "
                       f"Range: [{vs_pred.min():.1f}, {vs_pred.max():.1f}] μs/ft")
            return vs_pred, confidence
        else:
            logger.info(f"DTS prediction completed. Shape: {predictions.shape}, "
                       f"Range: [{predictions.min():.1f}, {predictions.max():.1f}] μs/ft")
            return predictions
    
    def predict_batch(
        self,
        input_batch: List[Union[np.ndarray, Dict[str, np.ndarray]]],
        batch_size: int = 8,
        return_confidence: bool = False,
        denormalize: bool = True
    ) -> Union[List[np.ndarray], tuple]:
        """
        Predict DTS values for a batch of wells
        
        Args:
            input_batch: List of input data for multiple wells
            batch_size: Processing batch size
            return_confidence: Whether to return prediction confidence
            denormalize: Whether to denormalize outputs
            
        Returns:
            List of DTS predictions, optionally with confidence estimates
        """
        logger.info(f"Processing batch of {len(input_batch)} wells for DTS prediction...")
        
        results = self.predictor.predict_batch(
            input_batch=input_batch,
            batch_size=batch_size,
            return_confidence=return_confidence,
            denormalize=denormalize
        )
        
        logger.info("Batch DTS prediction completed")
        return results
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        info = self.predictor.get_model_info()
        info.update({
            'predictor_type': 'VsPredictor',
            'target_curve': 'DTS',
            'specialization': 'Shear sonic (Vs) prediction',
            'expected_units': 'μs/ft',
            'physics_range': '(100, 1000) μs/ft'
        })
        return info
    
    def validate_input(
        self,
        input_data: Union[np.ndarray, Dict[str, np.ndarray]]
    ) -> Dict[str, Any]:
        """
        Validate input data for DTS prediction
        
        Args:
            input_data: Input well log data
            
        Returns:
            Validation results dictionary
        """
        return self.predictor.validate_input(input_data)
    
    def set_device(self, device: torch.device):
        """Move model to specified device"""
        self.predictor.set_device(device)
        logger.info(f"VsPredictor moved to device: {device}")
    
    @property
    def device(self) -> torch.device:
        """Get current device"""
        return self.predictor.device
    
    @property
    def is_trained(self) -> bool:
        """Check if model is trained/loaded"""
        return self.predictor.is_trained


# Convenience function for quick DTS prediction
def predict_vs(
    input_data: Union[np.ndarray, Dict[str, np.ndarray]],
    model_path: str,
    device: Optional[torch.device] = None,
    return_confidence: bool = False
) -> Union[np.ndarray, tuple]:
    """
    Convenience function for quick DTS (Vs) prediction
    
    Args:
        input_data: Input well log data
        model_path: Path to trained DTS model
        device: Computation device
        return_confidence: Whether to return confidence estimates
        
    Returns:
        DTS predictions in μs/ft
    """
    predictor = VsPredictor(model_path=model_path, device=device)
    return predictor.predict(input_data, return_confidence=return_confidence)


# Example usage and testing
if __name__ == "__main__":
    # Example of how to use the VsPredictor
    logging.basicConfig(level=logging.INFO)
    
    print("VsPredictor API Example")
    print("======================")
    
    try:
        # Initialize predictor (without actual model for this example)
        predictor = VsPredictor()
        
        # Display model info
        info = predictor.get_model_info()
        print("\\nModel Information:")
        for key, value in info.items():
            print(f"  {key}: {value}")
            
        print("\\nVsPredictor initialized successfully!")
        print("Ready for DTS (shear sonic) predictions.")
        
    except Exception as e:
        print(f"Example failed: {e}")
        print("Note: This is expected without actual model files.")
