#!/usr/bin/env python3
"""
Comprehensive integration test for init_vpvsdens system

This script provides a complete validation of the refactored prediction system
for Vp, Vs, and density, testing all major components and their integration.
"""

import sys
import torch
import numpy as np
from pathlib import Path
import logging

def setup_test_logging():
    """Setup logging for tests"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)

def test_complete_system_integration():
    """Test complete system integration for all curve types"""
    logger = setup_test_logging()
    
    print("=" * 80)
    print("INIT_VPVSDENS COMPREHENSIVE INTEGRATION TEST")
    print("=" * 80)
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    print(f"Working directory: {Path.cwd()}")
    
    test_results = {}
    
    # Test 1: Import System
    print("\n" + "=" * 60)
    print("TEST 1: IMPORT SYSTEM VALIDATION")
    print("=" * 60)
    
    try:
        # Core imports
        from init_vpvsdens.core.configs.curves import get_curve_config, CURVE_CONFIGURATIONS
        from init_vpvsdens.core.configs.training import get_training_template, get_integrated_template
        from init_vpvsdens.core.training.losses import create_vp_loss, create_shear_loss, create_density_loss
        from init_vpvsdens.core.training.checkpoint import get_device, save_checkpoint, load_checkpoint
        from init_vpvsdens.api.vs_predictor import VsPredictor
        
        print("✓ All core imports successful")
        test_results['imports'] = True
        
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        test_results['imports'] = False
        return test_results
    
    # Test 2: Configuration System
    print("\n" + "=" * 60)
    print("TEST 2: CONFIGURATION SYSTEM VALIDATION")
    print("=" * 60)
    
    try:
        # Test curve configurations for all three target types
        target_curves = ['AC', 'DTS', 'DEN']
        for curve in target_curves:
            config = get_curve_config(curve)
            print(f"✓ {curve}: {config['name']} ({config['unit']}) - Range: {config['physics_range']}")
        
        # Test training templates
        templates = ['vp_training', 'shear_training', 'density_training']
        for template in templates:
            config = get_training_template(template)
            print(f"✓ {template}: Target {config['target_curve']}, LR {config['learning_rate']}")
        
        # Test integrated templates
        integrated = ['standard_vp', 'standard_shear', 'standard_density']
        for template in integrated:
            config = get_integrated_template(template)
            print(f"✓ {template}: {config['description']}")
        
        print("✓ Configuration system validation successful")
        test_results['configuration'] = True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        test_results['configuration'] = False
    
    # Test 3: Loss Function System
    print("\n" + "=" * 60)
    print("TEST 3: LOSS FUNCTION SYSTEM VALIDATION")
    print("=" * 60)
    
    try:
        # Test loss functions for all curve types
        loss_creators = [
            ('AC', create_vp_loss, 'Vp prediction'),
            ('DTS', create_shear_loss, 'Vs prediction'),
            ('DEN', create_density_loss, 'Density prediction')
        ]
        
        for curve, creator, description in loss_creators:
            loss_fn = creator()
            print(f"✓ {description} loss: {loss_fn.loss_type}, constraints: {loss_fn.physics_constraints}")
            
            # Test with dummy data
            batch_size, seq_len = 2, 640
            predictions = torch.randn(batch_size, 1, seq_len)
            targets = torch.randn(batch_size, 1, seq_len)
            
            loss_components = loss_fn(predictions, targets)
            total_loss = loss_components['total_loss'].item()
            print(f"  Test loss computation: {total_loss:.4f}")
        
        print("✓ Loss function system validation successful")
        test_results['loss_functions'] = True
        
    except Exception as e:
        print(f"✗ Loss function test failed: {e}")
        test_results['loss_functions'] = False
    
    # Test 4: Checkpoint System
    print("\n" + "=" * 60)
    print("TEST 4: CHECKPOINT SYSTEM VALIDATION")
    print("=" * 60)
    
    try:
        # Test device detection
        device = get_device()
        print(f"✓ Device detection: {device}")
        
        # Test checkpoint save/load
        test_dir = Path("integration_test_checkpoints")
        test_dir.mkdir(exist_ok=True)
        
        dummy_state = {
            'model_state_dict': {'weight': torch.randn(10, 5)},
            'epoch': 100,
            'loss': 0.456
        }
        
        checkpoint_path = test_dir / "integration_test.pt"
        
        # Test save
        save_success = save_checkpoint(dummy_state, str(checkpoint_path))
        if save_success:
            print("✓ Checkpoint save successful")
        
        # Test load
        loaded_state, epoch, loss = load_checkpoint(str(checkpoint_path), device)
        if loaded_state is not None:
            print(f"✓ Checkpoint load successful: epoch {epoch}, loss {loss}")
        
        # Cleanup
        import shutil
        shutil.rmtree(test_dir, ignore_errors=True)
        
        print("✓ Checkpoint system validation successful")
        test_results['checkpoints'] = True
        
    except Exception as e:
        print(f"✗ Checkpoint test failed: {e}")
        test_results['checkpoints'] = False
    
    # Test 5: Training Script Framework
    print("\n" + "=" * 60)
    print("TEST 5: TRAINING SCRIPT FRAMEWORK VALIDATION")
    print("=" * 60)
    
    try:
        import subprocess
        
        # Test training script help
        result = subprocess.run([
            sys.executable, 'init_vpvsdens/train_vpvsdens.py', '--help'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ Training script help works")
        
        # Test argument validation
        result = subprocess.run([
            sys.executable, 'init_vpvsdens/train_vpvsdens.py', 
            '--curve', 'INVALID', '--data_file', 'nonexistent.hdf5'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0 and 'Unsupported curve' in result.stderr:
            print("✓ Training script validation works")
        
        print("✓ Training script framework validation successful")
        test_results['training_script'] = True
        
    except Exception as e:
        print(f"✗ Training script test failed: {e}")
        test_results['training_script'] = False
    
    # Test 6: Data File Availability
    print("\n" + "=" * 60)
    print("TEST 6: DATA FILE AVAILABILITY")
    print("=" * 60)
    
    try:
        data_files = [
            'init_vp_pred/A1.hdf5',
            'init_vp_pred/A2.hdf5',
            'init_density_base/A1.hdf5',
            'init_density_base/A2.hdf5'
        ]
        
        available_files = []
        for file_path in data_files:
            if Path(file_path).exists():
                print(f"✓ Available: {file_path}")
                available_files.append(file_path)
            else:
                print(f"✗ Missing: {file_path}")
        
        if available_files:
            print(f"✓ Data files available for testing: {len(available_files)} files")
            test_results['data_files'] = True
        else:
            print("⚠️  No data files available")
            test_results['data_files'] = False
        
    except Exception as e:
        print(f"✗ Data file check failed: {e}")
        test_results['data_files'] = False
    
    # Summary
    print("\n" + "=" * 80)
    print("INTEGRATION TEST SUMMARY")
    print("=" * 80)
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    for test_name, passed in test_results.items():
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\nOverall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL INTEGRATION TESTS PASSED!")
        print("The init_vpvsdens system is ready for use!")
    elif passed_tests >= total_tests * 0.8:
        print("\n✅ MOST INTEGRATION TESTS PASSED!")
        print("The init_vpvsdens system is largely functional with minor issues.")
    else:
        print("\n⚠️  SIGNIFICANT INTEGRATION ISSUES DETECTED")
        print("The init_vpvsdens system needs additional work before use.")
    
    return test_results

def main():
    """Run integration tests"""
    try:
        results = test_complete_system_integration()
        
        # Return appropriate exit code
        total_tests = len(results)
        passed_tests = sum(results.values())
        
        if passed_tests >= total_tests * 0.8:  # 80% pass rate
            return 0
        else:
            return 1
            
    except Exception as e:
        print(f"Integration test crashed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
