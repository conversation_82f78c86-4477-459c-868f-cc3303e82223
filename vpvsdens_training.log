2025-09-07 07:33:56,547 - __main__ - ERROR - Unsupported curve: INVALID
2025-09-07 07:33:56,547 - __main__ - ERROR - Supported curves: ['GR', 'AC', 'VP', 'DEN', 'CNL', 'NPHI', 'RLLD', 'RILD', 'PE', 'SP', 'CALI', 'DTS', 'VS', 'RHOB']
2025-09-07 07:34:11,995 - __main__ - ERROR - Data file not found: nonexistent.hdf5
2025-09-07 07:34:25,494 - __main__ - INFO - Using integrated template: standard_vp
2025-09-07 07:34:25,495 - __main__ - INFO - ============================================================
2025-09-07 07:34:25,495 - __main__ - INFO - STARTING VPVSDENS TRAINING
2025-09-07 07:34:25,496 - __main__ - INFO - ============================================================
2025-09-07 07:34:25,496 - __main__ - INFO - Target curve: AC
2025-09-07 07:34:25,496 - __main__ - INFO - Data file: init_vp_pred/A1.hdf5
2025-09-07 07:34:25,496 - __main__ - INFO - Output directory: test_output
2025-09-07 07:34:25,496 - __main__ - INFO - Learning rate: 0.0001
2025-09-07 07:34:25,496 - __main__ - INFO - Batch size: 8
2025-09-07 07:34:25,496 - __main__ - INFO - Max epochs: 200
2025-09-07 07:34:25,518 - init_vpvsdens.core.training.checkpoint - INFO - CUDA is available. Using GPU: cuda:0
2025-09-07 07:34:25,523 - init_vpvsdens.core.training.checkpoint - INFO - GPU Name: NVIDIA T550 Laptop GPU
2025-09-07 07:34:25,524 - init_vpvsdens.core.training.checkpoint - INFO - GPU Memory: 4.0 GB
2025-09-07 07:34:25,524 - __main__ - INFO - Using device: cuda:0
2025-09-07 07:34:25,524 - init_vpvsdens.core.training.losses - INFO - GeneralWellLogLoss initialized for 'AC' with mse loss
2025-09-07 07:34:25,525 - init_vpvsdens.core.training.losses - INFO -   Physics range: (40, 400)
2025-09-07 07:34:25,525 - init_vpvsdens.core.training.losses - INFO -   Constraint weight: 1.0
2025-09-07 07:34:25,525 - __main__ - INFO - Loss function created for AC:
2025-09-07 07:34:25,525 - __main__ - INFO -   Type: mse
2025-09-07 07:34:25,525 - __main__ - INFO -   Physics constraints: True
2025-09-07 07:34:25,525 - __main__ - INFO -   Constraint weight: 1.0
2025-09-07 07:34:25,526 - __main__ - ERROR - Training failed: VpTrainingManager.__init__() missing 1 required positional argument: 'output_dir'
2025-09-07 07:34:25,527 - __main__ - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_merged_vp_dens\init_vpvsdens\train_vpvsdens.py", line 262, in main
    results = run_training(config)
  File "C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_merged_vp_dens\init_vpvsdens\train_vpvsdens.py", line 182, in run_training
    training_manager = VpTrainingManager(vp_config)
TypeError: VpTrainingManager.__init__() missing 1 required positional argument: 'output_dir'

2025-09-07 07:35:08,196 - __main__ - INFO - Target curve: Acoustic/Sonic (μs/ft)
2025-09-07 07:35:08,196 - __main__ - INFO - Using integrated template: standard_vp
2025-09-07 07:35:08,196 - __main__ - INFO - ============================================================
2025-09-07 07:35:08,196 - __main__ - INFO - STARTING VPVSDENS TRAINING
2025-09-07 07:35:08,199 - __main__ - INFO - ============================================================
2025-09-07 07:35:08,199 - __main__ - INFO - Target curve: AC
2025-09-07 07:35:08,199 - __main__ - INFO - Data file: init_vp_pred/A1.hdf5
2025-09-07 07:35:08,199 - __main__ - INFO - Output directory: test_output
2025-09-07 07:35:08,199 - __main__ - INFO - Learning rate: 0.0001
2025-09-07 07:35:08,199 - __main__ - INFO - Batch size: 8
2025-09-07 07:35:08,199 - __main__ - INFO - Max epochs: 200
2025-09-07 07:35:08,218 - __main__ - INFO - Using device: cuda:0
2025-09-07 07:35:08,218 - __main__ - INFO - Loss function created for AC:
2025-09-07 07:35:08,218 - __main__ - INFO -   Type: mse
2025-09-07 07:35:08,218 - __main__ - INFO -   Physics constraints: True
2025-09-07 07:35:08,220 - __main__ - INFO -   Constraint weight: 1.0
2025-09-07 07:35:08,221 - __main__ - ERROR - Training failed: unsupported operand type(s) for /: 'str' and 'str'
2025-09-07 07:35:08,223 - __main__ - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_merged_vp_dens\init_vpvsdens\train_vpvsdens.py", line 273, in main
    results = run_training(config)
  File "C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_merged_vp_dens\init_vpvsdens\train_vpvsdens.py", line 193, in run_training
    training_manager = VpTrainingManager(vp_config, config['output_dir'])
  File "C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_merged_vp_dens\init_vpvsdens\api\..\..\init_vp_pred\train_vp_improved.py", line 382, in __init__
    self.training_dir = output_dir / 'training'
TypeError: unsupported operand type(s) for /: 'str' and 'str'

2025-09-07 07:35:18,336 - __main__ - INFO - Target curve: Shear Sonic Slowness (μs/ft)
2025-09-07 07:35:18,336 - __main__ - INFO - Using default template: shear_training
2025-09-07 07:35:18,336 - __main__ - INFO - ============================================================
2025-09-07 07:35:18,336 - __main__ - INFO - STARTING VPVSDENS TRAINING
2025-09-07 07:35:18,336 - __main__ - INFO - ============================================================
2025-09-07 07:35:18,336 - __main__ - INFO - Target curve: DTS
2025-09-07 07:35:18,336 - __main__ - INFO - Data file: init_vp_pred/A1.hdf5
2025-09-07 07:35:18,336 - __main__ - INFO - Output directory: test_output_dts
2025-09-07 07:35:18,336 - __main__ - INFO - Learning rate: 8e-05
2025-09-07 07:35:18,336 - __main__ - INFO - Batch size: 10
2025-09-07 07:35:18,336 - __main__ - INFO - Max epochs: 180
2025-09-07 07:35:18,360 - __main__ - INFO - Using device: cuda:0
2025-09-07 07:35:18,360 - __main__ - INFO - Loss function created for DTS:
2025-09-07 07:35:18,360 - __main__ - INFO -   Type: huber
2025-09-07 07:35:18,360 - __main__ - INFO -   Physics constraints: True
2025-09-07 07:35:18,360 - __main__ - INFO -   Constraint weight: 0.8
2025-09-07 07:35:18,360 - __main__ - ERROR - Training failed: unsupported operand type(s) for /: 'str' and 'str'
2025-09-07 07:35:18,360 - __main__ - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_merged_vp_dens\init_vpvsdens\train_vpvsdens.py", line 273, in main
    results = run_training(config)
  File "C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_merged_vp_dens\init_vpvsdens\train_vpvsdens.py", line 193, in run_training
    training_manager = VpTrainingManager(vp_config, config['output_dir'])
  File "C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\init_merged_vp_dens\init_vpvsdens\api\..\..\init_vp_pred\train_vp_improved.py", line 382, in __init__
    self.training_dir = output_dir / 'training'
TypeError: unsupported operand type(s) for /: 'str' and 'str'

2025-09-07 07:35:45,795 - __main__ - INFO - Target curve: Shear Sonic Slowness (μs/ft)
2025-09-07 07:35:45,795 - __main__ - INFO - Using default template: shear_training
2025-09-07 07:35:45,795 - __main__ - INFO - ============================================================
2025-09-07 07:35:45,795 - __main__ - INFO - STARTING VPVSDENS TRAINING
2025-09-07 07:35:45,795 - __main__ - INFO - ============================================================
2025-09-07 07:35:45,795 - __main__ - INFO - Target curve: DTS
2025-09-07 07:35:45,795 - __main__ - INFO - Data file: init_vp_pred/A1.hdf5
2025-09-07 07:35:45,795 - __main__ - INFO - Output directory: test_output_dts
2025-09-07 07:35:45,795 - __main__ - INFO - Learning rate: 8e-05
2025-09-07 07:35:45,795 - __main__ - INFO - Batch size: 10
2025-09-07 07:35:45,795 - __main__ - INFO - Max epochs: 180
2025-09-07 07:35:45,820 - __main__ - INFO - Using device: cuda:0
2025-09-07 07:35:45,821 - __main__ - INFO - Loss function created for DTS:
2025-09-07 07:35:45,822 - __main__ - INFO -   Type: huber
2025-09-07 07:35:45,822 - __main__ - INFO -   Physics constraints: True
2025-09-07 07:35:45,822 - __main__ - INFO -   Constraint weight: 0.8
2025-09-07 07:35:45,825 - __main__ - INFO - Training infrastructure setup complete
2025-09-07 07:35:45,825 - __main__ - INFO - ⚠️  Training framework setup complete
2025-09-07 07:35:45,825 - __main__ - INFO - 📝 Note: Full training implementation requires integration
2025-09-07 07:35:45,825 - __main__ - INFO -     of existing VpTrainingManager with new loss functions
2025-09-07 07:35:45,826 - __main__ - INFO - ============================================================
2025-09-07 07:35:45,826 - __main__ - INFO - TRAINING COMPLETED
2025-09-07 07:35:45,827 - __main__ - INFO - ============================================================
2025-09-07 07:35:45,827 - __main__ - INFO - Status: framework_ready
2025-09-07 07:35:45,827 - __main__ - INFO - Results saved to: test_output_dts
2025-09-07 07:36:53,773 - __main__ - ERROR - Unsupported curve: INVALID
2025-09-07 07:36:53,773 - __main__ - ERROR - Supported curves: ['GR', 'AC', 'VP', 'DEN', 'CNL', 'NPHI', 'RLLD', 'RILD', 'PE', 'SP', 'CALI', 'DTS', 'VS', 'RHOB']
