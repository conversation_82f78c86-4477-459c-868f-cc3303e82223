# Archives Directory

This directory contains intermediate files and artifacts from the VP/VS/density refactoring process.

## Directory Structure

### development_tests/
Contains test scripts used during the development and refactoring of the init_vpvsdens module:
- `test_vpvsdens_config.py` - Configuration system testing
- `test_vpvsdens_data_pipeline.py` - Data pipeline testing
- `test_vpvsdens_imports.py` - Import dependency testing
- `test_vpvsdens_integration.py` - Integration testing
- `test_vpvsdens_training.py` - Training functionality testing

### training_logs/
Contains training logs from development sessions:
- `vpvsdens_training.log` - Main training session logs

### test_outputs/
Contains test output directories from development:
- `test_output/` - General test output directory
- `test_output_dts/` - DTS (shear wave) specific test outputs

## Purpose

These files were moved here to clean up the workspace root directory while preserving all development artifacts for future reference. They represent the intermediate work done during the refactoring process to create the unified VP/VS/density prediction system according to the architecture specified in `2_Architecture_VpVsRho.md`.

## Note

These files are preserved for historical reference and debugging purposes. The active development should use the main `init_vpvsdens/` structure with proper output directories in `init_vpvsdens/outputs/`.
