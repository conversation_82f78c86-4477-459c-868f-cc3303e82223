#!/usr/bin/env python3
"""
Test script for init_vpvsdens training functionality

This script tests the training script setup and configuration system
without running full training loops.
"""

import sys
import subprocess
from pathlib import Path

def test_training_script_help():
    """Test that the training script shows help correctly"""
    print("=" * 60)
    print("TESTING TRAINING SCRIPT HELP")
    print("=" * 60)
    
    try:
        result = subprocess.run([
            sys.executable, 'init_vpvsdens/train_vpvsdens.py', '--help'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ Training script help displayed successfully")
            print("Help output preview:")
            lines = result.stdout.split('\n')[:10]
            for line in lines:
                print(f"  {line}")
            return True
        else:
            print(f"✗ Training script help failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ Training script help test failed: {e}")
        return False

def test_training_script_validation():
    """Test training script argument validation"""
    print("\n" + "=" * 60)
    print("TESTING TRAINING SCRIPT VALIDATION")
    print("=" * 60)
    
    # Test invalid curve
    print("🧪 Testing invalid curve validation...")
    try:
        result = subprocess.run([
            sys.executable, 'init_vpvsdens/train_vpvsdens.py', 
            '--curve', 'INVALID', '--data_file', 'nonexistent.hdf5'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0 and 'Unsupported curve' in result.stderr:
            print("✓ Invalid curve validation works")
        else:
            print(f"✗ Invalid curve validation failed")
            print(f"  Return code: {result.returncode}")
            print(f"  Stderr: {result.stderr}")
            
    except Exception as e:
        print(f"✗ Invalid curve test failed: {e}")
    
    # Test missing data file
    print("\n🧪 Testing missing data file validation...")
    try:
        result = subprocess.run([
            sys.executable, 'init_vpvsdens/train_vpvsdens.py', 
            '--curve', 'AC', '--data_file', 'nonexistent.hdf5'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0 and 'Data file not found' in result.stderr:
            print("✓ Missing data file validation works")
        else:
            print(f"✗ Missing data file validation failed")
            print(f"  Return code: {result.returncode}")
            print(f"  Stderr: {result.stderr}")
            
    except Exception as e:
        print(f"✗ Missing data file test failed: {e}")

def test_training_script_with_valid_args():
    """Test training script with valid arguments (dry run)"""
    print("\n" + "=" * 60)
    print("TESTING TRAINING SCRIPT WITH VALID ARGS")
    print("=" * 60)
    
    # Find available data file
    data_files = [
        'init_vp_pred/A1.hdf5',
        'init_density_base/A1.hdf5'
    ]
    
    available_file = None
    for file_path in data_files:
        if Path(file_path).exists():
            available_file = file_path
            break
    
    if not available_file:
        print("⚠️  No data files available for testing")
        return False
    
    print(f"📁 Using data file: {available_file}")
    
    # Test different curve types
    test_cases = [
        ('AC', 'standard_vp', 'Vp prediction'),
        ('DTS', 'standard_shear', 'Vs prediction'),
        ('DEN', 'standard_density', 'Density prediction')
    ]
    
    for curve, template, description in test_cases:
        print(f"\n🧪 Testing {description} ({curve})...")
        
        try:
            # Create a temporary output directory
            output_dir = f"test_output_{curve.lower()}"
            Path(output_dir).mkdir(exist_ok=True)
            
            result = subprocess.run([
                sys.executable, 'init_vpvsdens/train_vpvsdens.py',
                '--curve', curve,
                '--template', template,
                '--data_file', available_file,
                '--output_dir', output_dir,
                '--log_level', 'INFO'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print(f"✓ {description} setup successful")
                if 'framework_ready' in result.stdout:
                    print("  ✓ Training framework initialized")
                if 'TRAINING COMPLETED' in result.stdout:
                    print("  ✓ Training process completed")
            else:
                print(f"✗ {description} setup failed")
                print(f"  Return code: {result.returncode}")
                print(f"  Stderr: {result.stderr[:500]}...")
                
            # Cleanup
            import shutil
            shutil.rmtree(output_dir, ignore_errors=True)
            
        except subprocess.TimeoutExpired:
            print(f"⚠️  {description} test timed out (may be normal)")
        except Exception as e:
            print(f"✗ {description} test failed: {e}")

def test_configuration_integration():
    """Test that configurations integrate correctly"""
    print("\n" + "=" * 60)
    print("TESTING CONFIGURATION INTEGRATION")
    print("=" * 60)
    
    try:
        # Test that we can import and use the training script components
        sys.path.append('init_vpvsdens')
        
        from train_vpvsdens import create_training_config, setup_loss_function
        import argparse
        
        # Create mock arguments
        args = argparse.Namespace(
            curve='AC',
            template='standard_vp',
            data_file='init_vp_pred/A1.hdf5',
            output_dir='test_output',
            learning_rate=None,
            batch_size=None,
            epochs=None
        )
        
        # Test configuration creation
        config = create_training_config(args)
        print("✓ Training configuration created successfully")
        print(f"  Target curve: {config['target_curve']}")
        print(f"  Learning rate: {config['learning_rate']}")
        print(f"  Batch size: {config['batch_size']}")
        
        # Test loss function setup
        loss_fn = setup_loss_function(config['target_curve'], config)
        print("✓ Loss function setup successful")
        print(f"  Loss type: {loss_fn.loss_type}")
        print(f"  Target curve: {loss_fn.target_curve}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all training tests"""
    print("INIT_VPVSDENS TRAINING FUNCTIONALITY TEST")
    print("=" * 60)
    print(f"Python version: {sys.version}")
    print(f"Working directory: {Path.cwd()}")
    
    # Run all tests
    tests = [
        ("Training Script Help", test_training_script_help),
        ("Training Script Validation", test_training_script_validation),
        ("Training Script Valid Args", test_training_script_with_valid_args),
        ("Configuration Integration", test_configuration_integration)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"\n✗ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("TRAINING FUNCTIONALITY TEST SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 ALL TRAINING TESTS PASSED!")
        print("The init_vpvsdens training system is functional!")
    else:
        print("\n⚠️  SOME TRAINING TESTS FAILED")
        print("See details above for specific issues.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
